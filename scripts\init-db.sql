-- Restaurant Database Initialization Script
-- This script creates the basic database structure for the restaurant system

USE master;
GO

-- Create database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'restaurant_db')
BEGIN
    CREATE DATABASE restaurant_db;
END
GO

USE restaurant_db;
GO

-- Create Roles table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='roles' AND xtype='U')
BEGIN
    CREATE TABLE roles (
        role_id INT IDENTITY(1,1) PRIMARY KEY,
        role_name NVARCHAR(50) NOT NULL UNIQUE,
        description NVARCHAR(255),
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE()
    );
END
GO

-- Create Users table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='users' AND xtype='U')
BEGIN
    CREATE TABLE users (
        user_id INT IDENTITY(1,1) PRIMARY KEY,
        username NVARCHAR(50) NOT NULL UNIQUE,
        email NVARCHAR(100) NOT NULL UNIQUE,
        password_hash NVARCHAR(255) NOT NULL,
        full_name NVARCHAR(100),
        phone NVARCHAR(20),
        role_id INT NOT NULL,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (role_id) REFERENCES roles(role_id)
    );
END
GO

-- Create Categories table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='categories' AND xtype='U')
BEGIN
    CREATE TABLE categories (
        category_id INT IDENTITY(1,1) PRIMARY KEY,
        category_name NVARCHAR(100) NOT NULL,
        description NVARCHAR(255),
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE()
    );
END
GO

-- Create Foods table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='foods' AND xtype='U')
BEGIN
    CREATE TABLE foods (
        food_id INT IDENTITY(1,1) PRIMARY KEY,
        food_name NVARCHAR(100) NOT NULL,
        description NVARCHAR(500),
        price DECIMAL(10,2) NOT NULL,
        category_id INT NOT NULL,
        image_url NVARCHAR(255),
        is_available BIT DEFAULT 1,
        preparation_time INT DEFAULT 15, -- minutes
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (category_id) REFERENCES categories(category_id)
    );
END
GO

-- Create Tables table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='tables' AND xtype='U')
BEGIN
    CREATE TABLE tables (
        table_id INT IDENTITY(1,1) PRIMARY KEY,
        table_number NVARCHAR(10) NOT NULL UNIQUE,
        capacity INT NOT NULL,
        status NVARCHAR(20) DEFAULT 'available', -- available, occupied, reserved, maintenance
        qr_code NVARCHAR(255),
        qr_expires_at DATETIME2,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE()
    );
END
GO

-- Create Orders table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='orders' AND xtype='U')
BEGIN
    CREATE TABLE orders (
        order_id INT IDENTITY(1,1) PRIMARY KEY,
        table_id INT NOT NULL,
        customer_name NVARCHAR(100),
        customer_phone NVARCHAR(20),
        total_amount DECIMAL(10,2) DEFAULT 0,
        status NVARCHAR(20) DEFAULT 'pending', -- pending, confirmed, preparing, ready, served, paid, cancelled
        notes NVARCHAR(500),
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (table_id) REFERENCES tables(table_id)
    );
END
GO

-- Create Order Items table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='order_items' AND xtype='U')
BEGIN
    CREATE TABLE order_items (
        order_item_id INT IDENTITY(1,1) PRIMARY KEY,
        order_id INT NOT NULL,
        food_id INT NOT NULL,
        quantity INT NOT NULL,
        unit_price DECIMAL(10,2) NOT NULL,
        subtotal DECIMAL(10,2) NOT NULL,
        status NVARCHAR(20) DEFAULT 'pending', -- pending, preparing, ready, served
        notes NVARCHAR(255),
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (order_id) REFERENCES orders(order_id),
        FOREIGN KEY (food_id) REFERENCES foods(food_id)
    );
END
GO

-- Create Ingredients table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ingredients' AND xtype='U')
BEGIN
    CREATE TABLE ingredients (
        ingredient_id INT IDENTITY(1,1) PRIMARY KEY,
        ingredient_name NVARCHAR(100) NOT NULL,
        unit NVARCHAR(20) NOT NULL, -- kg, g, l, ml, piece, etc.
        current_stock DECIMAL(10,2) DEFAULT 0,
        min_stock DECIMAL(10,2) DEFAULT 0,
        max_stock DECIMAL(10,2) DEFAULT 0,
        unit_cost DECIMAL(10,2) DEFAULT 0,
        supplier NVARCHAR(100),
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE()
    );
END
GO

-- Create Food Ingredients table (many-to-many relationship)
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='food_ingredients' AND xtype='U')
BEGIN
    CREATE TABLE food_ingredients (
        food_ingredient_id INT IDENTITY(1,1) PRIMARY KEY,
        food_id INT NOT NULL,
        ingredient_id INT NOT NULL,
        quantity_required DECIMAL(10,2) NOT NULL,
        created_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (food_id) REFERENCES foods(food_id),
        FOREIGN KEY (ingredient_id) REFERENCES ingredients(ingredient_id),
        UNIQUE(food_id, ingredient_id)
    );
END
GO

-- Insert default roles
IF NOT EXISTS (SELECT * FROM roles WHERE role_name = 'Admin')
BEGIN
    INSERT INTO roles (role_name, description) VALUES 
    ('Admin', 'System Administrator with full access'),
    ('Staff', 'Restaurant staff member'),
    ('Chef', 'Kitchen chef with cooking responsibilities');
END
GO

-- Insert default admin user (password: admin123)
IF NOT EXISTS (SELECT * FROM users WHERE username = 'admin')
BEGIN
    INSERT INTO users (username, email, password_hash, full_name, role_id) VALUES 
    ('admin', '<EMAIL>', '$2b$10$rQZ8kHWKQOuXlY5qJ5Zv4eKjHgS7fGhI9mNpQrStUvWxYz1A2B3C4', 'System Administrator', 1);
END
GO

-- Insert sample categories
IF NOT EXISTS (SELECT * FROM categories WHERE category_name = 'Appetizers')
BEGIN
    INSERT INTO categories (category_name, description) VALUES 
    ('Appetizers', 'Starter dishes and small plates'),
    ('Main Courses', 'Primary dishes and entrees'),
    ('Desserts', 'Sweet treats and desserts'),
    ('Beverages', 'Drinks and beverages'),
    ('Soups', 'Hot and cold soups');
END
GO

-- Insert sample tables
IF NOT EXISTS (SELECT * FROM tables WHERE table_number = 'T001')
BEGIN
    INSERT INTO tables (table_number, capacity, status) VALUES 
    ('T001', 2, 'available'),
    ('T002', 4, 'available'),
    ('T003', 6, 'available'),
    ('T004', 2, 'available'),
    ('T005', 8, 'available');
END
GO

-- Insert sample ingredients
IF NOT EXISTS (SELECT * FROM ingredients WHERE ingredient_name = 'Rice')
BEGIN
    INSERT INTO ingredients (ingredient_name, unit, current_stock, min_stock, max_stock, unit_cost) VALUES 
    ('Rice', 'kg', 50.0, 10.0, 100.0, 2.50),
    ('Chicken Breast', 'kg', 20.0, 5.0, 50.0, 8.00),
    ('Tomatoes', 'kg', 15.0, 3.0, 30.0, 3.00),
    ('Onions', 'kg', 25.0, 5.0, 50.0, 1.50),
    ('Cooking Oil', 'l', 10.0, 2.0, 20.0, 4.00);
END
GO

PRINT 'Database initialization completed successfully!';
GO
