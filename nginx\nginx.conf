# Production Nginx Configuration for Restaurant Server
user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging format
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'rt=$request_time uct="$upstream_connect_time" '
                    'uht="$upstream_header_time" urt="$upstream_response_time"';

    access_log /var/log/nginx/access.log main;

    # Basic Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 50M;
    server_tokens off;

    # Gzip Settings
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_min_length 1000;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json
        image/svg+xml;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=1r/s;

    # Upstream for API Gateway
    upstream api_gateway {
        least_conn;
        server api-gateway:3000 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    # Upstream for services (if needed for direct access)
    upstream table_service {
        server table-service:3011 max_fails=3 fail_timeout=30s;
    }

    upstream menu_service {
        server menu-service:3002 max_fails=3 fail_timeout=30s;
    }

    upstream order_service {
        server order-service:3003 max_fails=3 fail_timeout=30s;
    }

    upstream kitchen_service {
        server kitchen-service:3004 max_fails=3 fail_timeout=30s;
    }

    upstream user_service {
        server user-service:3005 max_fails=3 fail_timeout=30s;
    }

    upstream inventory_service {
        server inventory-service:3006 max_fails=3 fail_timeout=30s;
    }

    upstream image_service {
        server image-service:3007 max_fails=3 fail_timeout=30s;
    }

    # HTTP Server (redirect to HTTPS in production)
    server {
        listen 80;
        server_name _;
        
        # Health check endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Redirect to HTTPS in production
        # return 301 https://$server_name$request_uri;

        # For development, serve directly
        location / {
            try_files $uri $uri/ @api_gateway;
        }

        # Static files for admin web
        location /admin {
            alias /usr/share/nginx/html;
            try_files $uri $uri/ /admin/index.html;
            expires 1h;
            add_header Cache-Control "public, immutable";
        }

        # API routes
        location /api/ {
            limit_req zone=api burst=20 nodelay;
            proxy_pass http://api_gateway;
            include /etc/nginx/proxy_params;
        }

        # Login endpoint with stricter rate limiting
        location /api/users/login {
            limit_req zone=login burst=5 nodelay;
            proxy_pass http://api_gateway;
            include /etc/nginx/proxy_params;
        }

        # WebSocket support for real-time features
        location /socket.io/ {
            proxy_pass http://api_gateway;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
        }

        # Fallback to API Gateway
        location @api_gateway {
            proxy_pass http://api_gateway;
            include /etc/nginx/proxy_params;
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    # HTTPS Server (uncomment for production with SSL)
    # server {
    #     listen 443 ssl http2;
    #     server_name your-domain.com;
    #     
    #     ssl_certificate /etc/nginx/ssl/cert.pem;
    #     ssl_certificate_key /etc/nginx/ssl/key.pem;
    #     ssl_session_timeout 1d;
    #     ssl_session_cache shared:SSL:50m;
    #     ssl_session_tickets off;
    #     
    #     # Modern configuration
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256:ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #     
    #     # HSTS
    #     add_header Strict-Transport-Security "max-age=63072000" always;
    #     
    #     # Security headers
    #     add_header X-Frame-Options DENY always;
    #     add_header X-Content-Type-Options nosniff always;
    #     add_header X-XSS-Protection "1; mode=block" always;
    #     add_header Referrer-Policy "no-referrer-when-downgrade" always;
    #     
    #     # Same configuration as HTTP server
    #     location / {
    #         try_files $uri $uri/ @api_gateway;
    #     }
    #     
    #     # ... (repeat other location blocks)
    # }
}

# Proxy parameters file
# This would be included in the main nginx.conf
