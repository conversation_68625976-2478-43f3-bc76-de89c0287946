# Docker Compose Override for Development
# This file extends docker-compose.yml for development environment
version: '3.8'

services:
  # API Gateway - Development overrides
  api-gateway:
    build:
      context: .
      target: api-gateway
    volumes:
      - ./api-gateway:/app/api-gateway
      - ./shared:/app/shared
      - /app/api-gateway/node_modules
    environment:
      - NODE_ENV=development
    command: ["npm", "run", "dev"]

  # Table Service - Development overrides
  table-service:
    volumes:
      - ./table-service:/app/table-service
      - ./shared:/app/shared
      - /app/table-service/node_modules
    environment:
      - NODE_ENV=development
    command: ["npm", "run", "dev"]

  # Menu Service - Development overrides
  menu-service:
    volumes:
      - ./menu-service:/app/menu-service
      - ./shared:/app/shared
      - /app/menu-service/node_modules
    environment:
      - NODE_ENV=development
    command: ["npm", "run", "dev"]

  # Order Service - Development overrides
  order-service:
    volumes:
      - ./order-service:/app/order-service
      - ./shared:/app/shared
      - /app/order-service/node_modules
    environment:
      - NODE_ENV=development
    command: ["npm", "run", "dev"]

  # Kitchen Service - Development overrides
  kitchen-service:
    volumes:
      - ./kitchen-service:/app/kitchen-service
      - ./shared:/app/shared
      - /app/kitchen-service/node_modules
    environment:
      - NODE_ENV=development
    command: ["npm", "run", "dev"]

  # User Service - Development overrides
  user-service:
    volumes:
      - ./user-service:/app/user-service
      - ./shared:/app/shared
      - /app/user-service/node_modules
    environment:
      - NODE_ENV=development
    command: ["npm", "run", "dev"]

  # Inventory Service - Development overrides
  inventory-service:
    volumes:
      - ./inventory-service:/app/inventory-service
      - ./shared:/app/shared
      - /app/inventory-service/node_modules
    environment:
      - NODE_ENV=development
    command: ["npm", "run", "dev"]

  # Image Service - Development overrides
  image-service:
    volumes:
      - ./image-service:/app/image-service
      - ./shared:/app/shared
      - /app/image-service/node_modules
      - ./image-service/uploads:/app/image-service/uploads
    environment:
      - NODE_ENV=development
    command: ["npm", "run", "dev"]

  # Admin Web - Development overrides
  admin-web:
    volumes:
      - ./admin-web:/usr/share/nginx/html:ro
