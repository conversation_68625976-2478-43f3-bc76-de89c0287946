# Production Docker Compose Configuration
version: '3.8'

services:
  # SQL Server Database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: restaurant-sqlserver-prod
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=${DB_PASSWORD:-Restaurant123!}
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data_prod:/var/opt/mssql
      - sqlserver_backup_prod:/var/opt/mssql/backup
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - restaurant-network-prod
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P ${DB_PASSWORD:-Restaurant123!} -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 2G
        reservations:
          memory: 1G

  # Redis for caching and sessions
  redis:
    image: redis:7-alpine
    container_name: restaurant-redis-prod
    command: redis-server --requirepass ${REDIS_PASSWORD:-redis123}
    ports:
      - "6379:6379"
    volumes:
      - redis_data_prod:/data
    networks:
      - restaurant-network-prod
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # API Gateway
  api-gateway:
    build:
      context: .
      target: api-gateway
    container_name: restaurant-api-gateway-prod
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - API_GATEWAY_PORT=3000
      - DB_SERVER=sqlserver
      - DB_DATABASE=${DB_DATABASE:-restaurant_db}
      - DB_USER=${DB_USER:-sa}
      - DB_PASSWORD=${DB_PASSWORD:-Restaurant123!}
      - DB_PORT=1433
      - JWT_SECRET=${JWT_SECRET:-restaurant_secret_key_prod}
      - JWT_EXPIRES_IN=24h
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis123}
    depends_on:
      sqlserver:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - restaurant-network-prod
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Load Balancer (Nginx)
  nginx:
    image: nginx:alpine
    container_name: restaurant-nginx-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - ./admin-web:/usr/share/nginx/html:ro
      - nginx_logs_prod:/var/log/nginx
    depends_on:
      - api-gateway
    networks:
      - restaurant-network-prod
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # Table Service
  table-service:
    build:
      context: .
      target: table-service
    container_name: restaurant-table-service-prod
    environment:
      - NODE_ENV=production
      - TABLE_SERVICE_PORT=3011
      - DB_SERVER=sqlserver
      - DB_DATABASE=${DB_DATABASE:-restaurant_db}
      - DB_USER=${DB_USER:-sa}
      - DB_PASSWORD=${DB_PASSWORD:-Restaurant123!}
      - DB_PORT=1433
      - JWT_SECRET=${JWT_SECRET:-restaurant_secret_key_prod}
      - QR_EXPIRATION_TIME=3600000
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis123}
    depends_on:
      sqlserver:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - restaurant-network-prod
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Menu Service
  menu-service:
    build:
      context: .
      target: menu-service
    container_name: restaurant-menu-service-prod
    environment:
      - NODE_ENV=production
      - MENU_SERVICE_PORT=3002
      - DB_SERVER=sqlserver
      - DB_DATABASE=${DB_DATABASE:-restaurant_db}
      - DB_USER=${DB_USER:-sa}
      - DB_PASSWORD=${DB_PASSWORD:-Restaurant123!}
      - DB_PORT=1433
      - JWT_SECRET=${JWT_SECRET:-restaurant_secret_key_prod}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis123}
    depends_on:
      sqlserver:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - restaurant-network-prod
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Order Service
  order-service:
    build:
      context: .
      target: order-service
    container_name: restaurant-order-service-prod
    environment:
      - NODE_ENV=production
      - ORDER_SERVICE_PORT=3003
      - DB_SERVER=sqlserver
      - DB_DATABASE=${DB_DATABASE:-restaurant_db}
      - DB_USER=${DB_USER:-sa}
      - DB_PASSWORD=${DB_PASSWORD:-Restaurant123!}
      - DB_PORT=1433
      - JWT_SECRET=${JWT_SECRET:-restaurant_secret_key_prod}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis123}
    depends_on:
      sqlserver:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - restaurant-network-prod
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Kitchen Service
  kitchen-service:
    build:
      context: .
      target: kitchen-service
    container_name: restaurant-kitchen-service-prod
    environment:
      - NODE_ENV=production
      - KITCHEN_SERVICE_PORT=3004
      - DB_SERVER=sqlserver
      - DB_DATABASE=${DB_DATABASE:-restaurant_db}
      - DB_USER=${DB_USER:-sa}
      - DB_PASSWORD=${DB_PASSWORD:-Restaurant123!}
      - DB_PORT=1433
      - JWT_SECRET=${JWT_SECRET:-restaurant_secret_key_prod}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis123}
    depends_on:
      sqlserver:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - restaurant-network-prod
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # User Service
  user-service:
    build:
      context: .
      target: user-service
    container_name: restaurant-user-service-prod
    environment:
      - NODE_ENV=production
      - USER_SERVICE_PORT=3005
      - DB_SERVER=sqlserver
      - DB_DATABASE=${DB_DATABASE:-restaurant_db}
      - DB_USER=${DB_USER:-sa}
      - DB_PASSWORD=${DB_PASSWORD:-Restaurant123!}
      - DB_PORT=1433
      - JWT_SECRET=${JWT_SECRET:-restaurant_secret_key_prod}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis123}
    depends_on:
      sqlserver:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - restaurant-network-prod
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Inventory Service
  inventory-service:
    build:
      context: .
      target: inventory-service
    container_name: restaurant-inventory-service-prod
    environment:
      - NODE_ENV=production
      - INVENTORY_SERVICE_PORT=3006
      - DB_SERVER=sqlserver
      - DB_DATABASE=${DB_DATABASE:-restaurant_db}
      - DB_USER=${DB_USER:-sa}
      - DB_PASSWORD=${DB_PASSWORD:-Restaurant123!}
      - DB_PORT=1433
      - JWT_SECRET=${JWT_SECRET:-restaurant_secret_key_prod}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis123}
    depends_on:
      sqlserver:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - restaurant-network-prod
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # Image Service
  image-service:
    build:
      context: .
      target: image-service
    container_name: restaurant-image-service-prod
    environment:
      - NODE_ENV=production
      - IMAGE_SERVICE_PORT=3007
      - DB_SERVER=sqlserver
      - DB_DATABASE=${DB_DATABASE:-restaurant_db}
      - DB_USER=${DB_USER:-sa}
      - DB_PASSWORD=${DB_PASSWORD:-Restaurant123!}
      - DB_PORT=1433
      - JWT_SECRET=${JWT_SECRET:-restaurant_secret_key_prod}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD:-redis123}
    depends_on:
      sqlserver:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - restaurant-network-prod
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro
      - image_uploads_prod:/app/image-service/uploads
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

volumes:
  sqlserver_data_prod:
    driver: local
  sqlserver_backup_prod:
    driver: local
  redis_data_prod:
    driver: local
  image_uploads_prod:
    driver: local
  nginx_logs_prod:
    driver: local

networks:
  restaurant-network-prod:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
