{"name": "express-http-proxy", "version": "2.1.1", "description": "http proxy middleware for express", "engines": {"node": ">=6.0.0"}, "main": "index.js", "scripts": {"test": "npm -s run mocha && npm run -s lint", "test:debug": "mocha inspect --debug-brk -R spec test --recursive --exit", "mocha": "mocha -R spec test --recursive --exit", "lint": "eslint index.js app/**/*js lib/*js"}, "repository": {"type": "git", "url": "git://github.com/villadora/express-http-proxy.git"}, "keywords": ["express-http-proxy"], "author": {"name": "villadora", "email": "<EMAIL>"}, "license": "MIT", "bugs": {"url": "https://github.com/villadora/express-http-proxy/issues"}, "devDependencies": {"body-parser": "^1.17.2", "chai": "^4.1.2", "cookie-parser": "^1.4.3", "eslint": "^8.48.0", "express": "^4.15.4", "mocha": "^10.2.0", "nock": "^13.3.3", "supertest": "^6.3.3"}, "dependencies": {"debug": "^3.0.1", "es6-promise": "^4.1.1", "raw-body": "^2.3.0"}, "contributors": [{"name": "<PERSON>"}, {"name": "eldereal", "url": "https://github.com/eldereal"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/razzmatazz"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://github.com/monkpow"}]}