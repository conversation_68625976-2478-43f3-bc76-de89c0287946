# Docker Environment Configuration for Restaurant Server
# This file contains environment variables for Docker deployment

# Database Configuration (Docker SQL Server)
DB_SERVER=sqlserver
DB_DATABASE=restaurant_db
DB_USER=sa
DB_PASSWORD=Restaurant123!
DB_PORT=1433

# Service Ports (Internal Docker Network)
API_GATEWAY_PORT=3000
TABLE_SERVICE_PORT=3011
MENU_SERVICE_PORT=3002
ORDER_SERVICE_PORT=3003
KITCHEN_SERVICE_PORT=3004
USER_SERVICE_PORT=3005
INVENTORY_SERVICE_PORT=3006
IMAGE_SERVICE_PORT=3007

# JWT Configuration
JWT_SECRET=restaurant_secret_key_docker_2024
JWT_EXPIRES_IN=24h

# QR Code Configuration
QR_EXPIRATION_TIME=3600000

# Node.js Environment
NODE_ENV=production

# Logging Configuration
LOG_LEVEL=info

# CORS Configuration
CORS_ORIGIN=*

# File Upload Configuration
MAX_FILE_SIZE=50mb
UPLOAD_PATH=/app/uploads

# Session Configuration
SESSION_SECRET=restaurant_session_secret_docker

# Email Configuration (if needed)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
SMTP_FROM=<EMAIL>

# Redis Configuration (if needed for caching)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# Monitoring Configuration
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Image Service Configuration
IMAGE_QUALITY=80
IMAGE_MAX_WIDTH=1920
IMAGE_MAX_HEIGHT=1080
THUMBNAIL_WIDTH=300
THUMBNAIL_HEIGHT=300

# Kitchen Service Configuration
ORDER_UPDATE_INTERVAL=5000
KITCHEN_DISPLAY_REFRESH=10000

# Table Service Configuration
QR_CODE_SIZE=200
QR_CODE_ERROR_CORRECTION=M

# Menu Service Configuration
MENU_CACHE_TTL=300000
CATEGORY_CACHE_TTL=600000

# Order Service Configuration
ORDER_TIMEOUT=1800000
ORDER_NOTIFICATION_ENABLED=true

# Inventory Service Configuration
LOW_STOCK_THRESHOLD=10
INVENTORY_UPDATE_INTERVAL=60000

# User Service Configuration
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_SPECIAL=true
PASSWORD_REQUIRE_NUMBER=true
PASSWORD_REQUIRE_UPPERCASE=true

# API Gateway Configuration
PROXY_TIMEOUT=30000
REQUEST_SIZE_LIMIT=50mb
ENABLE_COMPRESSION=true
