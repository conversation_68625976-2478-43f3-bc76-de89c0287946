version: '3.8'

services:
  # SQL Server Database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: restaurant-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=Restaurant123!
      - MSSQL_PID=Express
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - restaurant-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P Restaurant123! -Q 'SELECT 1'"]
      interval: 30s
      timeout: 10s
      retries: 5

  # API Gateway
  api-gateway:
    build:
      context: .
      target: api-gateway
    container_name: restaurant-api-gateway
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - API_GATEWAY_PORT=3000
      - DB_SERVER=sqlserver
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=Restaurant123!
      - DB_PORT=1433
      - JWT_SECRET=restaurant_secret_key_docker
      - JWT_EXPIRES_IN=24h
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - restaurant-network
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro

  # Table Service
  table-service:
    build:
      context: .
      target: table-service
    container_name: restaurant-table-service
    ports:
      - "3011:3011"
    environment:
      - NODE_ENV=production
      - TABLE_SERVICE_PORT=3011
      - DB_SERVER=sqlserver
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=Restaurant123!
      - DB_PORT=1433
      - JWT_SECRET=restaurant_secret_key_docker
      - QR_EXPIRATION_TIME=3600000
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - restaurant-network
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro

  # Menu Service
  menu-service:
    build:
      context: .
      target: menu-service
    container_name: restaurant-menu-service
    ports:
      - "3002:3002"
    environment:
      - NODE_ENV=production
      - MENU_SERVICE_PORT=3002
      - DB_SERVER=sqlserver
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=Restaurant123!
      - DB_PORT=1433
      - JWT_SECRET=restaurant_secret_key_docker
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - restaurant-network
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro

  # Order Service
  order-service:
    build:
      context: .
      target: order-service
    container_name: restaurant-order-service
    ports:
      - "3003:3003"
    environment:
      - NODE_ENV=production
      - ORDER_SERVICE_PORT=3003
      - DB_SERVER=sqlserver
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=Restaurant123!
      - DB_PORT=1433
      - JWT_SECRET=restaurant_secret_key_docker
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - restaurant-network
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro

  # Kitchen Service
  kitchen-service:
    build:
      context: .
      target: kitchen-service
    container_name: restaurant-kitchen-service
    ports:
      - "3004:3004"
    environment:
      - NODE_ENV=production
      - KITCHEN_SERVICE_PORT=3004
      - DB_SERVER=sqlserver
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=Restaurant123!
      - DB_PORT=1433
      - JWT_SECRET=restaurant_secret_key_docker
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - restaurant-network
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro

  # User Service
  user-service:
    build:
      context: .
      target: user-service
    container_name: restaurant-user-service
    ports:
      - "3005:3005"
    environment:
      - NODE_ENV=production
      - USER_SERVICE_PORT=3005
      - DB_SERVER=sqlserver
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=Restaurant123!
      - DB_PORT=1433
      - JWT_SECRET=restaurant_secret_key_docker
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - restaurant-network
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro

  # Inventory Service
  inventory-service:
    build:
      context: .
      target: inventory-service
    container_name: restaurant-inventory-service
    ports:
      - "3006:3006"
    environment:
      - NODE_ENV=production
      - INVENTORY_SERVICE_PORT=3006
      - DB_SERVER=sqlserver
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=Restaurant123!
      - DB_PORT=1433
      - JWT_SECRET=restaurant_secret_key_docker
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - restaurant-network
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro

  # Image Service
  image-service:
    build:
      context: .
      target: image-service
    container_name: restaurant-image-service
    ports:
      - "3007:3007"
    environment:
      - NODE_ENV=production
      - IMAGE_SERVICE_PORT=3007
      - DB_SERVER=sqlserver
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=Restaurant123!
      - DB_PORT=1433
      - JWT_SECRET=restaurant_secret_key_docker
    depends_on:
      sqlserver:
        condition: service_healthy
    networks:
      - restaurant-network
    restart: unless-stopped
    volumes:
      - ./shared:/app/shared:ro
      - image_uploads:/app/image-service/uploads

  # Admin Web Interface
  admin-web:
    build:
      context: .
      target: admin-web
    container_name: restaurant-admin-web
    ports:
      - "8080:80"
    depends_on:
      - api-gateway
    networks:
      - restaurant-network
    restart: unless-stopped

volumes:
  sqlserver_data:
    driver: local
  image_uploads:
    driver: local

networks:
  restaurant-network:
    driver: bridge
