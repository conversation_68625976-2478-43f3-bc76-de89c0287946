@echo off
REM Restaurant Server Docker Startup Script for Windows
REM This script helps you start the restaurant server system using Docker

setlocal enabledelayedexpansion

REM Function to print colored output (Windows doesn't support colors easily, so we'll use simple text)
:print_status
echo [INFO] %~1
goto :eof

:print_success
echo [SUCCESS] %~1
goto :eof

:print_warning
echo [WARNING] %~1
goto :eof

:print_error
echo [ERROR] %~1
goto :eof

REM Function to check if Dock<PERSON> is running
:check_docker
call :print_status "Checking Docker installation..."

docker --version >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker is not installed. Please install Docker Desktop first."
    exit /b 1
)

docker info >nul 2>&1
if errorlevel 1 (
    call :print_error "Docker is not running. Please start Docker Desktop."
    exit /b 1
)

call :print_success "Docker is running"
goto :eof

REM Function to check if Docker Compose is available
:check_docker_compose
call :print_status "Checking Docker Compose..."

docker-compose --version >nul 2>&1
if errorlevel 1 (
    docker compose version >nul 2>&1
    if errorlevel 1 (
        call :print_error "Docker Compose is not available. Please install Docker Compose."
        exit /b 1
    )
    set COMPOSE_CMD=docker compose
) else (
    set COMPOSE_CMD=docker-compose
)

call :print_success "Docker Compose is available"
goto :eof

REM Function to build and start services
:start_services
call :print_status "Building and starting Restaurant Server services..."

%COMPOSE_CMD% up --build -d
if errorlevel 1 (
    call :print_error "Failed to start services. Check the logs for more information."
    exit /b 1
)

call :print_success "All services started successfully!"
goto :eof

REM Function to show service status
:show_status
call :print_status "Checking service status..."
%COMPOSE_CMD% ps
goto :eof

REM Function to show logs
:show_logs
call :print_status "Showing service logs..."
%COMPOSE_CMD% logs -f
goto :eof

REM Function to stop services
:stop_services
call :print_status "Stopping Restaurant Server services..."
%COMPOSE_CMD% down
call :print_success "Services stopped"
goto :eof

REM Function to clean up
:cleanup
call :print_warning "This will remove all containers, networks, and volumes. Are you sure? (y/N)"
set /p response=
if /i "!response!"=="y" (
    call :print_status "Cleaning up..."
    %COMPOSE_CMD% down -v --remove-orphans
    docker image prune -f
    call :print_success "Cleanup completed"
) else (
    call :print_status "Cleanup cancelled"
)
goto :eof

REM Function to show help
:show_help
echo Restaurant Server Docker Management Script
echo.
echo Usage: %~nx0 [COMMAND]
echo.
echo Commands:
echo   start     Build and start all services (default)
echo   stop      Stop all services
echo   restart   Restart all services
echo   status    Show service status
echo   logs      Show service logs
echo   cleanup   Remove all containers, networks, and volumes
echo   help      Show this help message
echo.
echo Examples:
echo   %~nx0 start    # Start all services
echo   %~nx0 logs     # View logs
echo   %~nx0 cleanup  # Clean up everything
goto :eof

REM Function to show service URLs
:show_urls
call :print_success "Restaurant Server is running!"
echo.
echo Service URLs:
echo   Admin Web Interface: http://localhost:8080
echo   API Gateway:         http://localhost:3000
echo   Table Service:       http://localhost:3011
echo   Menu Service:        http://localhost:3002
echo   Order Service:       http://localhost:3003
echo   Kitchen Service:     http://localhost:3004
echo   User Service:        http://localhost:3005
echo   Inventory Service:   http://localhost:3006
echo   Image Service:       http://localhost:3007
echo.
echo Database:
echo   SQL Server:          localhost:1433
echo   Database:            restaurant_db
echo   Username:            sa
echo   Password:            Restaurant123!
echo.
echo To view logs: %~nx0 logs
echo To stop services: %~nx0 stop
goto :eof

REM Main script logic
set command=%1
if "%command%"=="" set command=start

if "%command%"=="start" (
    call :check_docker
    if errorlevel 1 exit /b 1
    call :check_docker_compose
    if errorlevel 1 exit /b 1
    call :start_services
    if errorlevel 1 exit /b 1
    call :show_urls
) else if "%command%"=="stop" (
    call :stop_services
) else if "%command%"=="restart" (
    call :check_docker
    if errorlevel 1 exit /b 1
    call :check_docker_compose
    if errorlevel 1 exit /b 1
    call :stop_services
    call :start_services
    if errorlevel 1 exit /b 1
    call :show_urls
) else if "%command%"=="status" (
    call :show_status
) else if "%command%"=="logs" (
    call :show_logs
) else if "%command%"=="cleanup" (
    call :cleanup
) else if "%command%"=="help" (
    call :show_help
) else if "%command%"=="--help" (
    call :show_help
) else if "%command%"=="-h" (
    call :show_help
) else (
    call :print_error "Unknown command: %command%"
    call :show_help
    exit /b 1
)

endlocal
