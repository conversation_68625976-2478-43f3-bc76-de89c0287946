# Hệ thống Microservice Nhà hàng

Hệ thống quản lý nhà hàng với kiến trúc microservice, hỗ trợ chức năng quét QR code để đặt món.

## Cấu trúc dự án

```
restaurant-server/
├── api-gateway/         # API Gateway Service
├── table-service/       # Quản lý bàn và QR code
├── menu-service/        # Quản lý danh mục và món ăn
├── order-service/       # Quản lý đơn hàng
├── kitchen-service/     # Quản lý hàng đợi nhà bếp
├── user-service/        # Quản lý người dùng và phân quyền
├── inventory-service/   # Quản lý nguyên liệu
├── shared/              # Các module dùng chung
├── .env                 # Cấu hình môi trường
└── package.json         # Cấu hình dự án
```

## Yêu cầu hệ thống

- Node.js v22.14.0 hoặc cao hơn
- SQL Server (SQLEXPRESS)
- Database restaurant_db

## Cài đặt

### Cách 1: Sử dụng Docker (Khuyến nghị)

1. Cài đặt Docker Desktop từ: https://www.docker.com/products/docker-desktop

2. Clone repository:
```bash
git clone <repository-url>
cd restaurant-server
```

3. Khởi động hệ thống bằng Docker:
```bash
# Windows
scripts\start-docker.bat

# macOS/Linux
chmod +x scripts/start-docker.sh
./scripts/start-docker.sh

# Hoặc sử dụng Makefile
make dev
```

4. Truy cập ứng dụng:
- Admin Web: http://localhost:8080
- API Gateway: http://localhost:3000

### Cách 2: Cài đặt truyền thống

1. Clone repository:
```bash
git clone <repository-url>
cd restaurant-server
```

2. Cài đặt các dependencies:
```bash
npm run setup
```

3. Cấu hình file .env (đã được tạo sẵn với các thông tin mặc định)

4. Khởi động hệ thống:
```bash
npm start
```

## Các Service

### API Gateway (Port 3000)
- Điểm vào chính của hệ thống
- Xử lý routing đến các service khác

### Table Service (Port 3001)
- Quản lý thông tin bàn
- Tạo và quản lý QR code cho bàn
- Xử lý trạng thái bàn

### Menu Service (Port 3002)
- Quản lý danh mục món ăn
- Quản lý thông tin món ăn
- Quản lý liên kết giữa món ăn và nguyên liệu

### Order Service (Port 3003)
- Quản lý đơn hàng
- Xử lý đặt món và cập nhật đơn hàng
- Giao tiếp realtime với khách hàng và nhà bếp

### Kitchen Service (Port 3004)
- Quản lý hàng đợi nhà bếp
- Cập nhật trạng thái món ăn
- Thông báo khi món ăn hoàn thành

### User Service (Port 3005)
- Quản lý người dùng và phân quyền
- Xác thực và cấp token
- Quản lý thông tin cá nhân

### Inventory Service (Port 3006)
- Quản lý nguyên liệu
- Cập nhật số lượng nguyên liệu
- Cảnh báo khi nguyên liệu sắp hết

## Luồng hoạt động QR Code

1. Nhân viên tạo QR code cho bàn trống
2. Khách hàng quét QR code để truy cập menu
3. Khách hàng đặt món thông qua QR code
4. Đơn hàng được gửi đến nhà bếp
5. Nhà bếp cập nhật trạng thái món ăn
6. Khách hàng nhận thông báo khi món ăn hoàn thành
7. Nhân viên cập nhật trạng thái đơn hàng khi khách thanh toán
8. QR code tự động vô hiệu hóa khi đơn hàng được thanh toán

## API Endpoints

Tài liệu API chi tiết có thể được truy cập tại:
- API Gateway: http://localhost:3000

## Phân quyền

Hệ thống có 3 vai trò chính:
1. Admin (role_id = 1): Toàn quyền quản lý hệ thống
2. Nhân viên (role_id = 2): Quản lý đơn hàng, bàn, menu
3. Đầu bếp (role_id = 3): Quản lý nhà bếp và nguyên liệu

## Docker Commands

### Khởi động nhanh
```bash
# Development mode
make dev

# Production mode
make prod

# Xem logs
make logs

# Dừng services
make down

# Clean up
make clean
```

### Chi tiết
Xem [README-Docker.md](README-Docker.md) để biết thêm chi tiết về Docker deployment.

## Troubleshooting

### Docker Issues
- Đảm bảo Docker Desktop đang chạy
- Kiểm tra port conflicts: `docker ps`
- Restart Docker Desktop nếu cần
- Xem logs: `make logs`

### Database Issues
- Kiểm tra SQL Server container: `make logs-db`
- Backup/restore: `make db-backup` / `make db-restore`
- Connect to DB: `make db-shell`
