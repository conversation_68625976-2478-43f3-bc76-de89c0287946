<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quản lý Nhà hàng</title>
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/menu-style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
</head>
<body>
    <div class="container">
        <!-- Sidebar Overlay -->
        <div class="sidebar-overlay" id="sidebar-overlay"></div>

        <!-- Sidebar -->
        <div class="sidebar" id="sidebar">
            <div class="logo">
                <h2>Nhà Hàng</h2>
                <button class="sidebar-toggle" id="sidebar-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
            <ul class="nav">
                <li class="nav-item active" data-page="dashboard" data-tooltip="Tổng quan">
                    <a href="#"><i class="fas fa-tachometer-alt"></i> <span>Tổng quan</span></a>
                </li>
                <li class="nav-item" data-page="tables" data-tooltip="Quản lý bàn">
                    <a href="#"><i class="fas fa-table"></i> <span>Quản lý bàn</span></a>
                </li>
                <li class="nav-item" data-page="menu" data-tooltip="Quản lý menu">
                    <a href="#"><i class="fas fa-utensils"></i> <span>Quản lý menu</span></a>
                </li>
                <li class="nav-item" data-page="orders" data-tooltip="Đơn hàng">
                    <a href="#"><i class="fas fa-shopping-cart"></i> <span>Đơn hàng</span></a>
                </li>
                <li class="nav-item" data-page="kitchen" data-tooltip="Nhà bếp">
                    <a href="#"><i class="fas fa-fire"></i> <span>Nhà bếp</span></a>
                </li>
                <li class="nav-item" data-page="inventory" data-tooltip="Kho hàng">
                    <a href="#"><i class="fas fa-boxes"></i> <span>Kho hàng</span></a>
                </li>
                <li class="nav-item" data-page="users" data-tooltip="Người dùng">
                    <a href="#"><i class="fas fa-users"></i> <span>Người dùng</span></a>
                </li>
            </ul>
        </div>

        <!-- Main Content -->
        <div class="main-content" id="main-content">
            <!-- Header -->
            <div class="header">
                <div class="header-left">
                    <button class="mobile-toggle" id="mobile-toggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="search-bar">
                        <input type="text" placeholder="Tìm kiếm...">
                        <button><i class="fas fa-search"></i></button>
                    </div>
                </div>
                <div class="user-info">
                    <span class="user-name">Admin</span>
                    <div class="user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="dropdown">
                        <button class="dropdown-toggle"><i class="fas fa-caret-down"></i></button>
                        <div class="dropdown-menu">
                            <a href="#"><i class="fas fa-user-cog"></i> Hồ sơ</a>
                            <a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Đăng xuất</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Page Content -->
            <div class="page-content">
                <!-- Dashboard Page -->
                <div class="page no-animations" id="dashboard">
                    <h2>Tổng quan</h2>
                    <div class="stats-container no-animations">
                        <div class="stat-card no-animations">
                            <div class="stat-icon" style="background-color: #4CAF50;">
                                <i class="fas fa-table"></i>
                            </div>
                            <div class="stat-info">
                                <h3>Bàn đang phục vụ</h3>
                                <p class="stat-number" id="active-tables">0</p>
                            </div>
                        </div>
                        <div class="stat-card no-animations">
                            <div class="stat-icon" style="background-color: #2196F3;">
                                <i class="fas fa-shopping-cart"></i>
                            </div>
                            <div class="stat-info">
                                <h3>Đơn hàng hôm nay</h3>
                                <p class="stat-number" id="today-orders">0</p>
                            </div>
                        </div>
                        <div class="stat-card no-animations">
                            <div class="stat-icon" style="background-color: #FF9800;">
                                <i class="fas fa-fire"></i>
                            </div>
                            <div class="stat-info">
                                <h3>Món ăn đang chế biến</h3>
                                <p class="stat-number" id="cooking-items">0</p>
                            </div>
                        </div>
                        <div class="stat-card no-animations">
                            <div class="stat-icon" style="background-color: #F44336;">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-info">
                                <h3>Nguyên liệu sắp hết</h3>
                                <p class="stat-number" id="low-inventory">0</p>
                            </div>
                        </div>
                    </div>
                    <div class="recent-orders">
                        <h3>Đơn hàng gần đây</h3>
                        <table>
                            <thead>
                                <tr>
                                    <th>Mã đơn</th>
                                    <th>Bàn</th>
                                    <th>Thời gian</th>
                                    <th>Tổng tiền</th>
                                    <th>Trạng thái</th>
                                </tr>
                            </thead>
                            <tbody id="recent-orders-table">
                                <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Tables Page -->
                <div class="page" id="tables" style="display: none;">
                    <h2>Quản lý bàn</h2>
                    <div class="action-bar">
                        <div class="action-buttons">
                            <button class="btn primary" id="add-table-btn"><i class="fas fa-plus"></i> Thêm bàn mới</button>
                        </div>
                        <div class="search-filter">
                            <div class="search-box">
                                <input type="text" id="table-search" placeholder="Tìm kiếm bàn...">
                                <button id="table-search-btn"><i class="fas fa-search"></i></button>
                            </div>
                            <div class="filter">
                                <label for="table-status-filter">
                                    <i class="fas fa-filter"></i> Lọc theo:
                                </label>
                                <select id="table-status-filter">
                                    <option value="all">Tất cả trạng thái</option>
                                    <option value="Trống">Trống</option>
                                    <option value="Đang phục vụ">Đang phục vụ</option>
                                </select>
                            </div>
                            <div class="filter">
                                <select id="table-location-filter">
                                    <option value="all">Tất cả vị trí</option>
                                    <option value="Tầng 1">Tầng 1</option>
                                    <option value="Tầng 2">Tầng 2</option>
                                    <option value="Sân thượng">Sân thượng</option>
                                    <option value="Ngoài trời">Ngoài trời</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="tables-grid" id="tables-container">
                        <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                    </div>
                </div>

                <!-- Menu Page -->
                <div class="page" id="menu" style="display: none;">
                    <h2>Quản lý menu</h2>
                    <div class="action-bar">
                        <div class="action-buttons">
                            <button class="btn primary" id="add-food-btn">
                                <i class="fas fa-plus"></i> Thêm món ăn
                            </button>
                            <button class="btn secondary" id="manage-categories-btn">
                                <i class="fas fa-tags"></i> Quản lý danh mục
                            </button>
                        </div>
                        <div class="filter">
                            <label for="category-filter">
                                <i class="fas fa-filter"></i> Lọc theo danh mục:
                            </label>
                            <select id="category-filter">
                                <option value="all">Tất cả danh mục</option>
                                <!-- Danh mục sẽ được thêm bằng JavaScript -->
                            </select>
                        </div>
                    </div>
                    <div class="menu-grid" id="menu-container">
                        <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                    </div>
                </div>

                <!-- Orders Page -->
                <div class="page" id="orders" style="display: none;">
                    <h2>Quản lý đơn hàng</h2>
                    <div class="action-bar">
                        <div class="filter">
                            <select id="order-status-filter">
                                <option value="all">Tất cả trạng thái</option>
                                <option value="Đang phục vụ">Đang phục vụ</option>
                                <option value="Đã thanh toán">Đã thanh toán</option>
                            </select>
                        </div>
                        <div class="date-filter">
                            <label>Từ: <input type="date" id="from-date"></label>
                            <label>Đến: <input type="date" id="to-date"></label>
                            <button class="btn secondary" id="apply-date-filter">Áp dụng</button>
                        </div>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Mã đơn</th>
                                <th>Bàn</th>
                                <th>Thời gian</th>
                                <th>Tổng tiền</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody id="orders-table">
                            <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                        </tbody>
                    </table>
                </div>

                <!-- Kitchen Page -->
                <div class="page" id="kitchen" style="display: none;">
                    <h2>Quản lý nhà bếp</h2>
                    <div class="action-bar">
                        <div class="action-buttons">
                            <button class="btn secondary" id="refresh-kitchen-btn">
                                <i class="fas fa-sync-alt"></i> Làm mới
                            </button>
                            <button class="btn warning" id="clear-completed-btn">
                                <i class="fas fa-trash"></i> Xóa món đã hoàn thành
                            </button>
                        </div>
                    </div>
                    <div class="kitchen-stats">
                        <div class="stat-pill waiting">
                            <span class="label">Chờ chế biến:</span>
                            <span class="value" id="waiting-count">0</span>
                        </div>
                        <div class="stat-pill cooking">
                            <span class="label">Đang chế biến:</span>
                            <span class="value" id="cooking-count">0</span>
                        </div>
                        <div class="stat-pill completed">
                            <span class="label">Hoàn thành:</span>
                            <span class="value" id="completed-count">0</span>
                        </div>
                    </div>
                    <div class="kitchen-queue">
                        <div class="queue-column">
                            <h3>Chờ chế biến</h3>
                            <div class="queue-items" id="waiting-items">
                                <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                            </div>
                        </div>
                        <div class="queue-column">
                            <h3>Đang chế biến</h3>
                            <div class="queue-items" id="cooking-items-list">
                                <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                            </div>
                        </div>
                        <div class="queue-column">
                            <h3>Hoàn thành</h3>
                            <div class="queue-items" id="completed-items">
                                <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Inventory Page -->
                <div class="page" id="inventory" style="display: none;">
                    <h2>Quản lý kho hàng</h2>
                    <div class="action-bar">
                        <button class="btn primary" id="add-ingredient-btn"><i class="fas fa-plus"></i> Thêm nguyên liệu</button>
                        <button class="btn warning" id="low-stock-btn"><i class="fas fa-exclamation-triangle"></i> Nguyên liệu sắp hết</button>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Tên nguyên liệu</th>
                                <th>Đơn vị</th>
                                <th>Số lượng</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody id="ingredients-table">
                            <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                        </tbody>
                    </table>
                </div>

                <!-- Users Page -->
                <div class="page" id="users" style="display: none;">
                    <h2>Quản lý người dùng</h2>
                    <div class="action-bar">
                        <button class="btn primary" id="add-user-btn"><i class="fas fa-user-plus"></i> Thêm người dùng</button>
                    </div>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Tên đăng nhập</th>
                                <th>Họ tên</th>
                                <th>Vai trò</th>
                                <th>Email</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody id="users-table">
                            <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <div class="modal" id="login-modal">
        <div class="modal-content">
            <h2>Đăng nhập</h2>
            <form id="login-form">
                <div class="form-group">
                    <label for="username">Tên đăng nhập</label>
                    <input type="text" id="username" required>
                </div>
                <div class="form-group">
                    <label for="password">Mật khẩu</label>
                    <input type="password" id="password" required>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn primary">Đăng nhập</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal thêm/sửa món ăn -->
    <div class="modal" id="food-modal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2 id="food-modal-title">Thêm món ăn mới</h2>
            <form id="food-form">
                <input type="hidden" id="food-id">
                <div class="form-group">
                    <label for="food-name">Tên món ăn</label>
                    <input type="text" id="food-name" required>
                </div>
                <div class="form-group">
                    <label for="food-category">Danh mục</label>
                    <select id="food-category" required>
                        <!-- Danh mục sẽ được thêm bằng JavaScript -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="food-price">Giá (VNĐ)</label>
                    <input type="number" id="food-price" min="0" step="1000" required>
                </div>
                <div class="form-group">
                    <label for="food-description">Mô tả</label>
                    <textarea id="food-description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="food-image-upload">Hình ảnh</label>
                    <div class="image-upload-container">
                        <input type="file" id="food-image-upload" accept="image/*">
                        <div class="image-preview" id="food-image-preview">
                            <img id="food-image-preview-img" src="img/default-food.jpg" alt="Xem trước">
                        </div>
                        <input type="hidden" id="food-image">
                    </div>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn secondary" id="cancel-food-btn">Hủy</button>
                    <button type="submit" class="btn primary">Lưu</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal quản lý danh mục -->
    <div class="modal" id="categories-modal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2>Quản lý danh mục</h2>
            <div class="categories-list" id="categories-list">
                <!-- Danh sách danh mục sẽ được thêm bằng JavaScript -->
            </div>
            <form id="category-form">
                <input type="hidden" id="category-id">
                <div class="form-group">
                    <label for="category-name">Tên danh mục</label>
                    <input type="text" id="category-name" required>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn secondary" id="cancel-category-btn">Hủy</button>
                    <button type="submit" class="btn primary" id="save-category-btn">Thêm danh mục</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal xác nhận xóa -->
    <div class="modal" id="confirm-modal">
        <div class="modal-content">
            <h2>Xác nhận</h2>
            <p id="confirm-message">Bạn có chắc chắn muốn xóa?</p>
            <div class="form-actions">
                <button type="button" class="btn secondary" id="cancel-confirm-btn">Hủy</button>
                <button type="button" class="btn danger" id="confirm-btn">Xóa</button>
            </div>
        </div>
    </div>

    <!-- Modal thêm/sửa người dùng -->
    <div class="modal" id="user-modal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2 id="user-modal-title">Thêm người dùng mới</h2>
            <form id="user-form">
                <input type="hidden" id="user-id">
                <div class="form-group">
                    <label for="user-username">Tên đăng nhập</label>
                    <input type="text" id="user-username" required>
                </div>
                <div class="form-group password-group">
                    <label for="user-password">Mật khẩu</label>
                    <input type="password" id="user-password" required>
                </div>
                <div class="form-group">
                    <label for="user-role">Vai trò</label>
                    <select id="user-role" required>
                        <option value="1">Admin</option>
                        <option value="2">Nhân viên</option>
                        <option value="3">Đầu bếp</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="user-fullname">Họ tên</label>
                    <input type="text" id="user-fullname">
                </div>
                <div class="form-group">
                    <label for="user-email">Email</label>
                    <input type="email" id="user-email">
                </div>
                <div class="form-group">
                    <label for="user-phone">Số điện thoại</label>
                    <input type="text" id="user-phone">
                </div>
                <div class="form-group">
                    <label for="user-age">Tuổi</label>
                    <input type="number" id="user-age" min="18" max="100">
                </div>
                <div class="form-group">
                    <label for="user-address">Địa chỉ</label>
                    <textarea id="user-address" rows="2"></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn secondary" id="cancel-user-btn">Hủy</button>
                    <button type="submit" class="btn primary">Lưu</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal thêm/sửa nguyên liệu -->
    <div class="modal" id="ingredient-modal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2 id="ingredient-modal-title">Thêm nguyên liệu mới</h2>
            <form id="ingredient-form">
                <input type="hidden" id="ingredient-id">
                <div class="form-group">
                    <label for="ingredient-name">Tên nguyên liệu</label>
                    <input type="text" id="ingredient-name" required>
                </div>
                <div class="form-group">
                    <label for="ingredient-unit">Đơn vị</label>
                    <input type="text" id="ingredient-unit" required>
                </div>
                <div class="form-group">
                    <label for="ingredient-quantity">Số lượng</label>
                    <input type="number" id="ingredient-quantity" min="0" step="0.01" required>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn secondary" id="cancel-ingredient-btn">Hủy</button>
                    <button type="submit" class="btn primary">Lưu</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal chi tiết đơn hàng -->
    <div class="modal" id="order-detail-modal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2>Chi tiết đơn hàng <span id="order-detail-id"></span></h2>
            <div class="order-info">
                <div class="order-info-row">
                    <div class="order-info-label">Bàn:</div>
                    <div class="order-info-value" id="order-detail-table"></div>
                </div>
                <div class="order-info-row">
                    <div class="order-info-label">Thời gian:</div>
                    <div class="order-info-value" id="order-detail-time"></div>
                </div>
                <div class="order-info-row">
                    <div class="order-info-label">Trạng thái:</div>
                    <div class="order-info-value" id="order-detail-status"></div>
                </div>
            </div>
            <h3>Danh sách món ăn</h3>
            <table class="data-table">
                <thead>
                    <tr>
                        <th>Tên món</th>
                        <th>Số lượng</th>
                        <th>Đơn giá</th>
                        <th>Thành tiền</th>
                    </tr>
                </thead>
                <tbody id="order-detail-items">
                    <!-- Danh sách món ăn sẽ được thêm bằng JavaScript -->
                </tbody>
            </table>
            <div class="order-total">
                <div class="order-total-row">
                    <div class="order-total-label">Tổng cộng:</div>
                    <div class="order-total-value" id="order-detail-total"></div>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn secondary" id="close-order-detail-btn">Đóng</button>
                <button type="button" class="btn primary" id="print-order-detail-btn">In đơn hàng</button>
            </div>
        </div>
    </div>

    <!-- Modal in đơn hàng -->
    <div class="modal" id="print-preview-modal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2>Xem trước khi in</h2>
            <div class="print-preview" id="print-preview-content">
                <!-- Nội dung xem trước khi in sẽ được thêm bằng JavaScript -->
            </div>
            <div class="form-actions">
                <button type="button" class="btn secondary" id="close-print-preview-btn">Đóng</button>
                <button type="button" class="btn primary" id="confirm-print-btn">In</button>
            </div>
        </div>
    </div>

    <!-- Modal thêm/sửa bàn -->
    <div class="modal" id="table-modal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2 id="table-modal-title">Thêm bàn mới</h2>
            <form id="table-form">
                <input type="hidden" id="table-id">
                <div class="form-group">
                    <label for="table-name">Tên bàn</label>
                    <input type="text" id="table-name" required>
                </div>
                <div class="form-group">
                    <label for="table-capacity">Sức chứa</label>
                    <input type="number" id="table-capacity" min="1" max="20" value="4" required>
                </div>
                <div class="form-group">
                    <label for="table-status">Trạng thái</label>
                    <select id="table-status" required>
                        <option value="Trống">Trống</option>
                        <option value="Đang phục vụ">Đang phục vụ</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="table-location">Vị trí</label>
                    <select id="table-location">
                        <option value="Tầng 1">Tầng 1</option>
                        <option value="Tầng 2">Tầng 2</option>
                        <option value="Sân thượng">Sân thượng</option>
                        <option value="Ngoài trời">Ngoài trời</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="table-note">Ghi chú</label>
                    <textarea id="table-note" rows="2"></textarea>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn secondary" id="cancel-table-btn">Hủy</button>
                    <button type="submit" class="btn primary">Lưu</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Modal chi tiết bàn -->
    <div class="modal" id="table-detail-modal">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2>Chi tiết bàn <span id="table-detail-name"></span></h2>
            <div class="table-info">
                <div class="table-info-row">
                    <div class="table-info-label">Sức chứa:</div>
                    <div class="table-info-value" id="table-detail-capacity"></div>
                </div>
                <div class="table-info-row">
                    <div class="table-info-label">Trạng thái:</div>
                    <div class="table-info-value" id="table-detail-status"></div>
                </div>
                <div class="table-info-row">
                    <div class="table-info-label">Vị trí:</div>
                    <div class="table-info-value" id="table-detail-location"></div>
                </div>
                <div class="table-info-row">
                    <div class="table-info-label">Ghi chú:</div>
                    <div class="table-info-value" id="table-detail-note"></div>
                </div>
            </div>
            <div id="table-current-order" style="display: none;">
                <h3>Đơn hàng hiện tại</h3>
                <div class="order-info">
                    <div class="order-info-row">
                        <div class="order-info-label">Mã đơn:</div>
                        <div class="order-info-value" id="table-order-id"></div>
                    </div>
                    <div class="order-info-row">
                        <div class="order-info-label">Thời gian:</div>
                        <div class="order-info-value" id="table-order-time"></div>
                    </div>
                    <div class="order-info-row">
                        <div class="order-info-label">Tổng tiền:</div>
                        <div class="order-info-value" id="table-order-total"></div>
                    </div>
                </div>
            </div>
            <div class="form-actions">
                <button type="button" class="btn secondary" id="close-table-detail-btn">Đóng</button>
                <button type="button" class="btn success" id="generate-qr-btn">
                    <i class="fas fa-qrcode"></i> Tạo QR Code
                </button>
                <button type="button" class="btn warning" id="edit-table-btn">Chỉnh sửa</button>
                <button type="button" class="btn primary" id="view-table-order-btn" style="display: none;">Xem đơn hàng</button>
                <button type="button" class="btn danger" id="delete-table-btn">Xóa bàn</button>
            </div>
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
