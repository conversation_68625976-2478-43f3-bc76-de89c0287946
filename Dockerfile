# Multi-stage Dockerfile for Restaurant Microservices
FROM node:18-alpine AS base

# Install dependencies for native modules and security updates
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    dumb-init \
    && ln -sf python3 /usr/bin/python \
    && apk upgrade --no-cache

# Create app user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S restaurant -u 1001

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY shared/package*.json ./shared/

# Install dependencies
RUN npm ci --only=production --silent && \
    cd shared && npm ci --only=production --silent && \
    npm cache clean --force

# Copy shared modules
COPY shared/ ./shared/

# Change ownership to app user
RUN chown -R restaurant:nodejs /app

# API Gateway Stage
FROM base AS api-gateway
WORKDIR /app/api-gateway
COPY api-gateway/package*.json ./
RUN npm ci --only=production --silent && npm cache clean --force
COPY api-gateway/ ./
RUN chown -R restaurant:nodejs /app/api-gateway
USER restaurant
EXPOSE 3000
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node -e "require('http').get('http://localhost:3000/', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"
CMD ["dumb-init", "node", "index.js"]

# Table Service Stage
FROM base AS table-service
WORKDIR /app/table-service
COPY table-service/package*.json ./
RUN npm ci --only=production
COPY table-service/ ./
EXPOSE 3011
CMD ["node", "index.js"]

# Menu Service Stage
FROM base AS menu-service
WORKDIR /app/menu-service
COPY menu-service/package*.json ./
RUN npm ci --only=production
COPY menu-service/ ./
EXPOSE 3002
CMD ["node", "index.js"]

# Order Service Stage
FROM base AS order-service
WORKDIR /app/order-service
COPY order-service/package*.json ./
RUN npm ci --only=production
COPY order-service/ ./
EXPOSE 3003
CMD ["node", "index.js"]

# Kitchen Service Stage
FROM base AS kitchen-service
WORKDIR /app/kitchen-service
COPY kitchen-service/package*.json ./
RUN npm ci --only=production
COPY kitchen-service/ ./
EXPOSE 3004
CMD ["node", "index.js"]

# User Service Stage
FROM base AS user-service
WORKDIR /app/user-service
COPY user-service/package*.json ./
RUN npm ci --only=production
COPY user-service/ ./
EXPOSE 3005
CMD ["node", "index.js"]

# Inventory Service Stage
FROM base AS inventory-service
WORKDIR /app/inventory-service
COPY inventory-service/package*.json ./
RUN npm ci --only=production
COPY inventory-service/ ./
EXPOSE 3006
CMD ["node", "index.js"]

# Image Service Stage
FROM base AS image-service
WORKDIR /app/image-service
COPY image-service/package*.json ./
RUN npm ci --only=production
COPY image-service/ ./
# Create uploads directory
RUN mkdir -p uploads/foods uploads/users uploads/temp
EXPOSE 3007
CMD ["node", "index.js"]

# Admin Web Stage (Nginx for static files)
FROM nginx:alpine AS admin-web
COPY admin-web/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
