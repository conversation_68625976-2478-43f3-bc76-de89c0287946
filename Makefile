# Restaurant Server Docker Management Makefile
.PHONY: help build up down restart logs status clean dev prod backup restore

# Default target
.DEFAULT_GOAL := help

# Variables
COMPOSE_FILE := docker-compose.yml
COMPOSE_FILE_DEV := docker-compose.yml -f docker-compose.override.yml
COMPOSE_FILE_PROD := docker-compose.prod.yml
PROJECT_NAME := restaurant-server

# Colors for output
YELLOW := \033[1;33m
GREEN := \033[0;32m
RED := \033[0;31m
NC := \033[0m # No Color

help: ## Show this help message
	@echo "$(YELLOW)Restaurant Server Docker Management$(NC)"
	@echo ""
	@echo "$(GREEN)Available commands:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(GREEN)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Development commands
dev: ## Start development environment with hot reload
	@echo "$(YELLOW)Starting development environment...$(NC)"
	docker-compose -f $(COMPOSE_FILE_DEV) up -d
	@echo "$(GREEN)Development environment started!$(NC)"
	@$(MAKE) show-urls

dev-build: ## Build and start development environment
	@echo "$(YELLOW)Building and starting development environment...$(NC)"
	docker-compose -f $(COMPOSE_FILE_DEV) up --build -d
	@echo "$(GREEN)Development environment built and started!$(NC)"
	@$(MAKE) show-urls

dev-down: ## Stop development environment
	@echo "$(YELLOW)Stopping development environment...$(NC)"
	docker-compose -f $(COMPOSE_FILE_DEV) down
	@echo "$(GREEN)Development environment stopped!$(NC)"

# Production commands
prod: ## Start production environment
	@echo "$(YELLOW)Starting production environment...$(NC)"
	docker-compose -f $(COMPOSE_FILE_PROD) up -d
	@echo "$(GREEN)Production environment started!$(NC)"
	@$(MAKE) show-urls-prod

prod-build: ## Build and start production environment
	@echo "$(YELLOW)Building and starting production environment...$(NC)"
	docker-compose -f $(COMPOSE_FILE_PROD) up --build -d
	@echo "$(GREEN)Production environment built and started!$(NC)"
	@$(MAKE) show-urls-prod

prod-down: ## Stop production environment
	@echo "$(YELLOW)Stopping production environment...$(NC)"
	docker-compose -f $(COMPOSE_FILE_PROD) down
	@echo "$(GREEN)Production environment stopped!$(NC)"

# Basic commands
build: ## Build all services
	@echo "$(YELLOW)Building all services...$(NC)"
	docker-compose build
	@echo "$(GREEN)Build completed!$(NC)"

up: ## Start all services
	@echo "$(YELLOW)Starting all services...$(NC)"
	docker-compose up -d
	@echo "$(GREEN)All services started!$(NC)"
	@$(MAKE) show-urls

down: ## Stop all services
	@echo "$(YELLOW)Stopping all services...$(NC)"
	docker-compose down
	@echo "$(GREEN)All services stopped!$(NC)"

restart: ## Restart all services
	@echo "$(YELLOW)Restarting all services...$(NC)"
	docker-compose restart
	@echo "$(GREEN)All services restarted!$(NC)"

# Monitoring commands
logs: ## Show logs for all services
	docker-compose logs -f

logs-api: ## Show API Gateway logs
	docker-compose logs -f api-gateway

logs-db: ## Show database logs
	docker-compose logs -f sqlserver

logs-nginx: ## Show Nginx logs (production)
	docker-compose -f $(COMPOSE_FILE_PROD) logs -f nginx

status: ## Show status of all services
	@echo "$(YELLOW)Service Status:$(NC)"
	docker-compose ps

health: ## Check health of all services
	@echo "$(YELLOW)Health Check:$(NC)"
	@docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep restaurant

# Maintenance commands
clean: ## Remove all containers, networks, and volumes
	@echo "$(RED)WARNING: This will remove all containers, networks, and volumes!$(NC)"
	@read -p "Are you sure? (y/N): " confirm && [ "$$confirm" = "y" ] || exit 1
	@echo "$(YELLOW)Cleaning up...$(NC)"
	docker-compose down -v --remove-orphans
	docker system prune -f
	@echo "$(GREEN)Cleanup completed!$(NC)"

clean-images: ## Remove all restaurant server images
	@echo "$(YELLOW)Removing restaurant server images...$(NC)"
	docker images | grep restaurant | awk '{print $$3}' | xargs -r docker rmi -f
	@echo "$(GREEN)Images removed!$(NC)"

reset: ## Complete reset (clean + rebuild)
	@$(MAKE) clean
	@$(MAKE) build
	@$(MAKE) up

# Database commands
db-backup: ## Backup database
	@echo "$(YELLOW)Creating database backup...$(NC)"
	docker exec restaurant-sqlserver /opt/mssql-tools/bin/sqlcmd \
		-S localhost -U sa -P Restaurant123! \
		-Q "BACKUP DATABASE restaurant_db TO DISK = '/var/opt/mssql/backup/restaurant_db_$$(date +%Y%m%d_%H%M%S).bak'"
	@echo "$(GREEN)Database backup created!$(NC)"

db-restore: ## Restore database from backup (specify BACKUP_FILE)
	@if [ -z "$(BACKUP_FILE)" ]; then \
		echo "$(RED)Please specify BACKUP_FILE. Example: make db-restore BACKUP_FILE=restaurant_db_20240101_120000.bak$(NC)"; \
		exit 1; \
	fi
	@echo "$(YELLOW)Restoring database from $(BACKUP_FILE)...$(NC)"
	docker exec restaurant-sqlserver /opt/mssql-tools/bin/sqlcmd \
		-S localhost -U sa -P Restaurant123! \
		-Q "RESTORE DATABASE restaurant_db FROM DISK = '/var/opt/mssql/backup/$(BACKUP_FILE)' WITH REPLACE"
	@echo "$(GREEN)Database restored!$(NC)"

db-shell: ## Connect to database shell
	docker exec -it restaurant-sqlserver /opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P Restaurant123!

# Service-specific commands
restart-api: ## Restart API Gateway
	docker-compose restart api-gateway

restart-db: ## Restart database
	docker-compose restart sqlserver

scale-api: ## Scale API Gateway (specify REPLICAS)
	@if [ -z "$(REPLICAS)" ]; then \
		echo "$(RED)Please specify REPLICAS. Example: make scale-api REPLICAS=3$(NC)"; \
		exit 1; \
	fi
	docker-compose up -d --scale api-gateway=$(REPLICAS)

# Utility commands
shell-api: ## Open shell in API Gateway container
	docker exec -it restaurant-api-gateway /bin/sh

shell-db: ## Open shell in database container
	docker exec -it restaurant-sqlserver /bin/bash

show-urls: ## Show service URLs
	@echo ""
	@echo "$(GREEN)🍽️  Restaurant Server is running!$(NC)"
	@echo ""
	@echo "$(YELLOW)Service URLs:$(NC)"
	@echo "  Admin Web Interface: http://localhost:8080"
	@echo "  API Gateway:         http://localhost:3000"
	@echo "  Table Service:       http://localhost:3011"
	@echo "  Menu Service:        http://localhost:3002"
	@echo "  Order Service:       http://localhost:3003"
	@echo "  Kitchen Service:     http://localhost:3004"
	@echo "  User Service:        http://localhost:3005"
	@echo "  Inventory Service:   http://localhost:3006"
	@echo "  Image Service:       http://localhost:3007"
	@echo ""
	@echo "$(YELLOW)Database:$(NC)"
	@echo "  SQL Server:          localhost:1433"
	@echo "  Database:            restaurant_db"
	@echo "  Username:            sa"
	@echo "  Password:            Restaurant123!"
	@echo ""

show-urls-prod: ## Show production service URLs
	@echo ""
	@echo "$(GREEN)🍽️  Restaurant Server (Production) is running!$(NC)"
	@echo ""
	@echo "$(YELLOW)Service URLs:$(NC)"
	@echo "  Main Application:    http://localhost"
	@echo "  Admin Interface:     http://localhost/admin"
	@echo "  API Endpoint:        http://localhost/api"
	@echo ""
	@echo "$(YELLOW)Database:$(NC)"
	@echo "  SQL Server:          localhost:1433"
	@echo "  Database:            restaurant_db"
	@echo "  Username:            sa"
	@echo "  Password:            Restaurant123!"
	@echo ""

# Development helpers
install: ## Install dependencies in all services
	@echo "$(YELLOW)Installing dependencies...$(NC)"
	npm install
	@for service in api-gateway table-service menu-service order-service kitchen-service user-service inventory-service image-service shared; do \
		echo "Installing dependencies for $$service..."; \
		cd $$service && npm install && cd ..; \
	done
	@echo "$(GREEN)Dependencies installed!$(NC)"

test: ## Run tests (if available)
	@echo "$(YELLOW)Running tests...$(NC)"
	# Add test commands here when tests are available
	@echo "$(GREEN)Tests completed!$(NC)"

lint: ## Run linting (if available)
	@echo "$(YELLOW)Running linting...$(NC)"
	# Add lint commands here when linting is configured
	@echo "$(GREEN)Linting completed!$(NC)"
