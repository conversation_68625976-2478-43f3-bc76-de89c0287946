# Docker Compose Configuration for Testing
version: '3.8'

services:
  # Test Database
  sqlserver-test:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: restaurant-sqlserver-test
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=TestPassword123!
      - MSSQL_PID=Express
    ports:
      - "1434:1433"
    volumes:
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    networks:
      - restaurant-test-network
    healthcheck:
      test: ["CMD-SHELL", "/opt/mssql-tools/bin/sqlcmd -S localhost -U sa -P TestPassword123! -Q 'SELECT 1'"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Redis for testing
  redis-test:
    image: redis:7-alpine
    container_name: restaurant-redis-test
    command: redis-server --requirepass testredis123
    ports:
      - "6380:6379"
    networks:
      - restaurant-test-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # API Gateway Test
  api-gateway-test:
    build:
      context: .
      target: api-gateway
    container_name: restaurant-api-gateway-test
    ports:
      - "3100:3000"
    environment:
      - NODE_ENV=test
      - API_GATEWAY_PORT=3000
      - DB_SERVER=sqlserver-test
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=TestPassword123!
      - DB_PORT=1433
      - JWT_SECRET=test_secret_key
      - JWT_EXPIRES_IN=1h
      - REDIS_HOST=redis-test
      - REDIS_PASSWORD=testredis123
    depends_on:
      sqlserver-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - restaurant-test-network
    volumes:
      - ./shared:/app/shared:ro
    command: ["dumb-init", "npm", "test"]

  # Table Service Test
  table-service-test:
    build:
      context: .
      target: table-service
    container_name: restaurant-table-service-test
    ports:
      - "3111:3011"
    environment:
      - NODE_ENV=test
      - TABLE_SERVICE_PORT=3011
      - DB_SERVER=sqlserver-test
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=TestPassword123!
      - DB_PORT=1433
      - JWT_SECRET=test_secret_key
      - QR_EXPIRATION_TIME=3600000
      - REDIS_HOST=redis-test
      - REDIS_PASSWORD=testredis123
    depends_on:
      sqlserver-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - restaurant-test-network
    volumes:
      - ./shared:/app/shared:ro
    command: ["dumb-init", "npm", "test"]

  # Menu Service Test
  menu-service-test:
    build:
      context: .
      target: menu-service
    container_name: restaurant-menu-service-test
    ports:
      - "3102:3002"
    environment:
      - NODE_ENV=test
      - MENU_SERVICE_PORT=3002
      - DB_SERVER=sqlserver-test
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=TestPassword123!
      - DB_PORT=1433
      - JWT_SECRET=test_secret_key
      - REDIS_HOST=redis-test
      - REDIS_PASSWORD=testredis123
    depends_on:
      sqlserver-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - restaurant-test-network
    volumes:
      - ./shared:/app/shared:ro
    command: ["dumb-init", "npm", "test"]

  # Order Service Test
  order-service-test:
    build:
      context: .
      target: order-service
    container_name: restaurant-order-service-test
    ports:
      - "3103:3003"
    environment:
      - NODE_ENV=test
      - ORDER_SERVICE_PORT=3003
      - DB_SERVER=sqlserver-test
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=TestPassword123!
      - DB_PORT=1433
      - JWT_SECRET=test_secret_key
      - REDIS_HOST=redis-test
      - REDIS_PASSWORD=testredis123
    depends_on:
      sqlserver-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - restaurant-test-network
    volumes:
      - ./shared:/app/shared:ro
    command: ["dumb-init", "npm", "test"]

  # Kitchen Service Test
  kitchen-service-test:
    build:
      context: .
      target: kitchen-service
    container_name: restaurant-kitchen-service-test
    ports:
      - "3104:3004"
    environment:
      - NODE_ENV=test
      - KITCHEN_SERVICE_PORT=3004
      - DB_SERVER=sqlserver-test
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=TestPassword123!
      - DB_PORT=1433
      - JWT_SECRET=test_secret_key
      - REDIS_HOST=redis-test
      - REDIS_PASSWORD=testredis123
    depends_on:
      sqlserver-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - restaurant-test-network
    volumes:
      - ./shared:/app/shared:ro
    command: ["dumb-init", "npm", "test"]

  # User Service Test
  user-service-test:
    build:
      context: .
      target: user-service
    container_name: restaurant-user-service-test
    ports:
      - "3105:3005"
    environment:
      - NODE_ENV=test
      - USER_SERVICE_PORT=3005
      - DB_SERVER=sqlserver-test
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=TestPassword123!
      - DB_PORT=1433
      - JWT_SECRET=test_secret_key
      - REDIS_HOST=redis-test
      - REDIS_PASSWORD=testredis123
    depends_on:
      sqlserver-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - restaurant-test-network
    volumes:
      - ./shared:/app/shared:ro
    command: ["dumb-init", "npm", "test"]

  # Inventory Service Test
  inventory-service-test:
    build:
      context: .
      target: inventory-service
    container_name: restaurant-inventory-service-test
    ports:
      - "3106:3006"
    environment:
      - NODE_ENV=test
      - INVENTORY_SERVICE_PORT=3006
      - DB_SERVER=sqlserver-test
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=TestPassword123!
      - DB_PORT=1433
      - JWT_SECRET=test_secret_key
      - REDIS_HOST=redis-test
      - REDIS_PASSWORD=testredis123
    depends_on:
      sqlserver-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - restaurant-test-network
    volumes:
      - ./shared:/app/shared:ro
    command: ["dumb-init", "npm", "test"]

  # Image Service Test
  image-service-test:
    build:
      context: .
      target: image-service
    container_name: restaurant-image-service-test
    ports:
      - "3107:3007"
    environment:
      - NODE_ENV=test
      - IMAGE_SERVICE_PORT=3007
      - DB_SERVER=sqlserver-test
      - DB_DATABASE=restaurant_db
      - DB_USER=sa
      - DB_PASSWORD=TestPassword123!
      - DB_PORT=1433
      - JWT_SECRET=test_secret_key
      - REDIS_HOST=redis-test
      - REDIS_PASSWORD=testredis123
    depends_on:
      sqlserver-test:
        condition: service_healthy
      redis-test:
        condition: service_healthy
    networks:
      - restaurant-test-network
    volumes:
      - ./shared:/app/shared:ro
    command: ["dumb-init", "npm", "test"]

networks:
  restaurant-test-network:
    driver: bridge
