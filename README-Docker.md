# Restaurant Server - Docker Deployment Guide

Hướng dẫn triển khai hệ thống Restaurant Server sử dụng Docker và Docker Compose.

## Yêu cầu hệ thống

- **Docker Desktop**: Phiê<PERSON> bản 4.0 trở lên
- **RAM**: T<PERSON><PERSON> thiểu 4GB (khuyến nghị 8GB)
- **Disk Space**: T<PERSON><PERSON> thiểu 5GB trống
- **OS**: Windows 10/11, macOS 10.15+, hoặc Linux

## Cài đặt Docker Desktop

### Windows
1. Tải Docker Desktop từ: https://www.docker.com/products/docker-desktop
2. Chạy file installer và làm theo hướng dẫn
3. Khởi động lại máy tính nếu được yêu cầu
4. Mở Docker Desktop và đợi khởi động hoàn tất

### macOS
1. Tải Docker Desktop từ: https://www.docker.com/products/docker-desktop
2. Kéo Docker.app vào thư mục Applications
3. Mở Docker từ Applications và làm theo hướng dẫn setup

### Linux
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER
```

## Khởi động nhanh

### Windows
```cmd
# Mở Command Prompt hoặc PowerShell trong thư mục dự án
scripts\start-docker.bat
```

### macOS/Linux
```bash
# Mở Terminal trong thư mục dự án
chmod +x scripts/start-docker.sh
./scripts/start-docker.sh
```

## Các lệnh Docker Compose

### Khởi động tất cả services
```bash
docker-compose up -d
```

### Xem logs của tất cả services
```bash
docker-compose logs -f
```

### Xem logs của một service cụ thể
```bash
docker-compose logs -f api-gateway
docker-compose logs -f sqlserver
```

### Dừng tất cả services
```bash
docker-compose down
```

### Rebuild và khởi động lại
```bash
docker-compose up --build -d
```

### Xóa tất cả containers và volumes
```bash
docker-compose down -v --remove-orphans
```

## Cấu trúc Services

| Service | Port | Mô tả |
|---------|------|-------|
| **admin-web** | 8080 | Giao diện quản trị web |
| **api-gateway** | 3000 | API Gateway chính |
| **table-service** | 3011 | Quản lý bàn và QR code |
| **menu-service** | 3002 | Quản lý menu và món ăn |
| **order-service** | 3003 | Quản lý đơn hàng |
| **kitchen-service** | 3004 | Quản lý nhà bếp |
| **user-service** | 3005 | Quản lý người dùng |
| **inventory-service** | 3006 | Quản lý kho |
| **image-service** | 3007 | Quản lý hình ảnh |
| **sqlserver** | 1433 | Cơ sở dữ liệu SQL Server |

## URLs truy cập

- **Admin Web**: http://localhost:8080
- **API Gateway**: http://localhost:3000
- **Database**: localhost:1433 (sa/Restaurant123!)

## Cấu hình Database

Database sẽ được tự động khởi tạo với:
- **Server**: localhost:1433
- **Database**: restaurant_db
- **Username**: sa
- **Password**: Restaurant123!

## Development Mode

Để chạy ở chế độ development với hot reload:

```bash
# Sử dụng override file
docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d
```

## Troubleshooting

### Lỗi port đã được sử dụng
```bash
# Kiểm tra port nào đang được sử dụng
netstat -an | findstr :3000  # Windows
lsof -i :3000                # macOS/Linux

# Dừng tất cả containers
docker-compose down
```

### Lỗi database connection
```bash
# Kiểm tra SQL Server container
docker-compose logs sqlserver

# Restart SQL Server
docker-compose restart sqlserver
```

### Lỗi build image
```bash
# Clean build cache
docker system prune -f
docker-compose build --no-cache
```

### Lỗi permission (Linux/macOS)
```bash
# Cấp quyền cho scripts
chmod +x scripts/*.sh

# Cấp quyền cho Docker socket
sudo usermod -aG docker $USER
```

## Monitoring và Logs

### Xem trạng thái containers
```bash
docker-compose ps
```

### Xem resource usage
```bash
docker stats
```

### Backup database
```bash
# Tạo backup
docker exec restaurant-sqlserver /opt/mssql-tools/bin/sqlcmd \
  -S localhost -U sa -P Restaurant123! \
  -Q "BACKUP DATABASE restaurant_db TO DISK = '/var/opt/mssql/backup/restaurant_db.bak'"

# Copy backup ra host
docker cp restaurant-sqlserver:/var/opt/mssql/backup/restaurant_db.bak ./backup/
```

## Production Deployment

### Cấu hình cho production
1. Sửa file `.env.docker` với thông tin production
2. Thay đổi passwords mặc định
3. Cấu hình SSL/TLS
4. Setup reverse proxy (Nginx/Apache)
5. Cấu hình monitoring và logging

### Security Checklist
- [ ] Thay đổi database password
- [ ] Thay đổi JWT secret
- [ ] Cấu hình firewall
- [ ] Enable HTTPS
- [ ] Setup backup tự động
- [ ] Cấu hình log rotation

## Scaling Services

### Scale một service cụ thể
```bash
# Scale API Gateway thành 3 instances
docker-compose up -d --scale api-gateway=3
```

### Load Balancer
Để scale production, cần setup load balancer (Nginx, HAProxy, etc.)

## Maintenance

### Update images
```bash
# Pull latest images
docker-compose pull

# Restart với images mới
docker-compose up -d
```

### Clean up
```bash
# Xóa unused images
docker image prune -f

# Xóa unused volumes
docker volume prune -f

# Xóa unused networks
docker network prune -f
```

## Support

Nếu gặp vấn đề:
1. Kiểm tra logs: `docker-compose logs -f`
2. Kiểm tra trạng thái: `docker-compose ps`
3. Restart services: `docker-compose restart`
4. Rebuild nếu cần: `docker-compose up --build -d`

## Useful Commands

```bash
# Vào shell của container
docker exec -it restaurant-api-gateway /bin/sh

# Xem logs realtime
docker-compose logs -f --tail=100

# Restart một service
docker-compose restart api-gateway

# Stop một service
docker-compose stop api-gateway

# Start một service
docker-compose start api-gateway
```
