# Restaurant Server Environment Configuration Template
# Copy this file to .env and update the values

# Database Configuration
DB_SERVER=localhost
DB_DATABASE=restaurant_db
DB_USER=sa
DB_PASSWORD=your_password_here
DB_PORT=1433

# Service Ports
API_GATEWAY_PORT=3000
TABLE_SERVICE_PORT=3011
MENU_SERVICE_PORT=3002
ORDER_SERVICE_PORT=3003
KITCHEN_SERVICE_PORT=3004
USER_SERVICE_PORT=3005
INVENTORY_SERVICE_PORT=3006
IMAGE_SERVICE_PORT=3007

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h

# QR Code Configuration
QR_EXPIRATION_TIME=3600000

# Node.js Environment
NODE_ENV=development

# Redis Configuration (optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# Email Configuration (optional)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
SMTP_FROM=<EMAIL>

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# File Upload Configuration
MAX_FILE_SIZE=50mb
UPLOAD_PATH=./uploads

# Logging Configuration
LOG_LEVEL=info

# CORS Configuration
CORS_ORIGIN=*
