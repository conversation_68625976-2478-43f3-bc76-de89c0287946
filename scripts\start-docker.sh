#!/bin/bash

# Restaurant Server Docker Startup Script
# This script helps you start the restaurant server system using Docker

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is running
check_docker() {
    print_status "Checking Docker installation..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker Desktop first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker Desktop."
        exit 1
    fi
    
    print_success "Docker is running"
}

# Function to check if Docker Compose is available
check_docker_compose() {
    print_status "Checking Docker Compose..."
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    
    print_success "Docker Compose is available"
}

# Function to build and start services
start_services() {
    print_status "Building and starting Restaurant Server services..."
    
    # Use docker compose if available, otherwise fall back to docker-compose
    if docker compose version &> /dev/null; then
        COMPOSE_CMD="docker compose"
    else
        COMPOSE_CMD="docker-compose"
    fi
    
    # Build and start services
    $COMPOSE_CMD up --build -d
    
    if [ $? -eq 0 ]; then
        print_success "All services started successfully!"
    else
        print_error "Failed to start services. Check the logs for more information."
        exit 1
    fi
}

# Function to show service status
show_status() {
    print_status "Checking service status..."
    
    if docker compose version &> /dev/null; then
        docker compose ps
    else
        docker-compose ps
    fi
}

# Function to show logs
show_logs() {
    print_status "Showing service logs..."
    
    if docker compose version &> /dev/null; then
        docker compose logs -f
    else
        docker-compose logs -f
    fi
}

# Function to stop services
stop_services() {
    print_status "Stopping Restaurant Server services..."
    
    if docker compose version &> /dev/null; then
        docker compose down
    else
        docker-compose down
    fi
    
    print_success "Services stopped"
}

# Function to clean up (remove containers, networks, volumes)
cleanup() {
    print_warning "This will remove all containers, networks, and volumes. Are you sure? (y/N)"
    read -r response
    
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_status "Cleaning up..."
        
        if docker compose version &> /dev/null; then
            docker compose down -v --remove-orphans
        else
            docker-compose down -v --remove-orphans
        fi
        
        # Remove unused images
        docker image prune -f
        
        print_success "Cleanup completed"
    else
        print_status "Cleanup cancelled"
    fi
}

# Function to show help
show_help() {
    echo "Restaurant Server Docker Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Build and start all services (default)"
    echo "  stop      Stop all services"
    echo "  restart   Restart all services"
    echo "  status    Show service status"
    echo "  logs      Show service logs"
    echo "  cleanup   Remove all containers, networks, and volumes"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start    # Start all services"
    echo "  $0 logs     # View logs"
    echo "  $0 cleanup  # Clean up everything"
}

# Function to show service URLs
show_urls() {
    print_success "Restaurant Server is running!"
    echo ""
    echo "Service URLs:"
    echo "  Admin Web Interface: http://localhost:8080"
    echo "  API Gateway:         http://localhost:3000"
    echo "  Table Service:       http://localhost:3011"
    echo "  Menu Service:        http://localhost:3002"
    echo "  Order Service:       http://localhost:3003"
    echo "  Kitchen Service:     http://localhost:3004"
    echo "  User Service:        http://localhost:3005"
    echo "  Inventory Service:   http://localhost:3006"
    echo "  Image Service:       http://localhost:3007"
    echo ""
    echo "Database:"
    echo "  SQL Server:          localhost:1433"
    echo "  Database:            restaurant_db"
    echo "  Username:            sa"
    echo "  Password:            Restaurant123!"
    echo ""
    echo "To view logs: $0 logs"
    echo "To stop services: $0 stop"
}

# Main script logic
case "${1:-start}" in
    start)
        check_docker
        check_docker_compose
        start_services
        show_urls
        ;;
    stop)
        stop_services
        ;;
    restart)
        check_docker
        check_docker_compose
        stop_services
        start_services
        show_urls
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    cleanup)
        cleanup
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        print_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
